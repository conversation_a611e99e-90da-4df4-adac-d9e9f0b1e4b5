version: '3.9' # Use a recent version

services:
  frontend:
    build:
      context: . # Root directory context
      dockerfile: Dockerfile.frontend # Specify the frontend Dockerfile
    ports:
      - "3000:3000" # Map host port 3000 to container port 3000
    restart: unless-stopped
    depends_on:
      - node-backend # Frontend might implicitly depend on backend API being available
      - instagram-api
    networks:
      - app-network

  node-backend:
    build:
      context: ./node_backend # Context is the node_backend directory
      dockerfile: Dockerfile # Uses Dockerfile within that context
    ports:
      - "3001:3001" # Map host port 3001 to container port 3001
    environment:
      # Use service name 'python-api' and internal port 8000
      PYTHON_BACKEND_URL: http://python-api:8000/generate_response 
      RETTIWT_API_KEY: a2R0PUk2NG5Ib0ZlWGE5Vnh3SEt6R3YwZzU2TDlJV01QeGtDZmk1RzRpYlU7YXV0aF90b2tlbj1kZThlZmQ1MzM2NWRjYjE0NzIzZDBmZjU1NmFkYTIxMWQxMWIyZTZhO2N0MD02YTQ5ZjliOWViZjZkYmQ5M2NmMTcyNGQ1NWVjYWUxMDQ4MDNkNjU1ZTdjOTYzYTcwNTZhMWJkMGQwMWI2Mjk0YWM0OTZjZjMzMWNhZjE5ODg4MzE1NDdhZTc2MjdjM2EyM2RiNWFjYThmYjBkZjY0N2E4MjJmMjUzZTJmMDE5ZjE4YTQwYWMzN2Q0MzliOTNlMzNmYWIzOWQ0NTQzZGQxO3R3aWQ9dSUzRDI3MTQ1ODA1NjQ7
      PORT: 3001 # Optional: Explicitly set port if needed by the app
    restart: unless-stopped
    depends_on:
      - python-api # Node backend needs the Python API to be ready
    env_file:
      - .env # Load variables from .env file in the root
    networks:
      - app-network

  instagram-api:
    build:
      context: ./instagram_video_downloader # Context is the instagram_backend directory
      dockerfile: Dockerfile # Uses Dockerfile within that context
    ports:
      - "3010:3010"
    restart: unless-stopped
    env_file:
      - .env # Load variables from .env file in the root
    volumes:
      - instagram_media_data:/tmp # Mount volume for temporary downloaded files
    networks:
      - app-network

  python-api:
    build:
      context: ./python_backend # Context is the python_backend directory
      dockerfile: Dockerfile # Uses Dockerfile within that context
    ports:
      - "8000:8000" # Map host port 8000 to container port 8000
    environment:
      OPENAI_API_KEY: ******************************************************** # Read from .env file or host environment
      # Add any other Python environment variables here if needed
    volumes:
      - tweet_media_data:/app/tweet_media
      - instagram_media_data:/app/tmp  # Mount to /app/tmp instead of /tmp for consistency
    restart: unless-stopped
    env_file:
      - .env # Load variables from .env file in the root
    networks:
      - app-network

  proxy:
    image: nginx:alpine
    ports:
      - "8080:80"  # We’ll expose this with ngrok
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - frontend
      - node-backend
    networks:
      - app-network

networks:
  app-network:
    driver: bridge # Use the default bridge driver

volumes:
  tweet_media_data: # Define the named volume for persisted media
  instagram_media_data: # Define the named volume for Instagram media
    driver: local  # Explicitly set the driver to local

